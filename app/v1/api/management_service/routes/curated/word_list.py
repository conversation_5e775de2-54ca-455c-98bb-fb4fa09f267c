from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, Optional
from bson import ObjectId
from datetime import datetime
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.schema.pagination import PaginationResponse
from app.shared.utils.mongodb import convert_object_ids


loggers = setup_new_logging(__name__)

router = APIRouter(
)
@router.get("/word-list", response_model=PaginationResponse[Dict[str, Any]])
async def word_list(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    start_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc or desc"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[Dict[str, Any]]:
    """
    Get a unique word list across all task items using MongoDB aggregation pipeline.
    """
    try:
        # Build base match conditions
        match_conditions = {
            "user_id": ObjectId(user_tenant.user.id),
            "type": {"$ne": "speak_word"},
        }

        # Add date filter if provided
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                    date_filter["$gte"] = start_dt
                except ValueError:
                    pass
            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
                    date_filter["$lte"] = end_dt
                except ValueError:
                    pass
            if date_filter:
                match_conditions["created_at"] = date_filter

        # Determine sort order
        sort_direction = -1 if sort_order == "desc" else 1

        # Build aggregation pipeline
        pipeline = [
            # Stage 1: Match documents based on filters
            {"$match": match_conditions},

            # Stage 2: Project only needed fields
            {
                "$project": {
                    "question.options_metadata": 1,
                    "question.options_en": 1
                }
            },

            # Stage 3: Convert options_metadata object to array of key-value pairs
            {
                "$addFields": {
                    "options_array": {"$objectToArray": "$question.options_metadata"},
                    "options_en": "$question.options_en"
                }
            },

            # Stage 4: Unwind the options array to process each option individually
            {
                "$unwind": {
                    "path": "$options_array",
                    "preserveNullAndEmptyArrays": False
                }
            },

            # Stage 5: Project the required fields for each option
            {
                "$project": {
                    "option_key": "$options_array.k",
                    "text": "$options_array.v.text",
                    "audio_url": "$options_array.v.audio_url",
                    "object_name": "$options_array.v.file_info.object_name",
                    "text_en": {
                        "$cond": [
                            {
                                "$and": [
                                    {"$ne": ["$options_en", None]},
                                    {"$ne": ["$options_en", {}]},
                                    {"$type": ["$options_en", "object"]}
                                ]
                            },
                            {
                                "$let": {
                                    "vars": {
                                        "matching_option": {
                                            "$filter": {
                                                "input": {"$objectToArray": "$options_en"},
                                                "as": "option_en",
                                                "cond": {"$eq": ["$$option_en.k", "$options_array.k"]}
                                            }
                                        }
                                    },
                                    "in": {
                                        "$cond": [
                                            {"$gt": [{"$size": "$$matching_option"}, 0]},
                                            {"$arrayElemAt": ["$$matching_option.v", 0]},
                                            None
                                        ]
                                    }
                                }
                            },
                            None
                        ]
                    }
                }
            },

            # Stage 6: Filter out null/empty texts and group by text to ensure uniqueness
            {
                "$match": {
                    "text": {"$ne": None, "$ne": ""}
                }
            },

            # Stage 7: Group by text to get unique words with their metadata
            {
                "$group": {
                    "_id": "$text",
                    "text": {"$first": "$text"},
                    "text_en": {"$first": "$text_en"},
                    "audio_url": {"$first": "$audio_url"},
                    "object_name": {"$first": "$object_name"}
                }
            },

            # Stage 8: Sort by text
            {"$sort": {"text": sort_direction}},

            # Stage 9: Add total count and paginate
            {
                "$facet": {
                    "data": [
                        {"$skip": (page - 1) * limit},
                        {"$limit": limit}
                    ],
                    "metadata": [
                        {"$count": "total"}
                    ]
                }
            }
        ]

        # Execute aggregation pipeline
        cursor = await user_tenant.async_db.task_items.aggregate(pipeline)
        result_list = await cursor.to_list(length=1)

        # Extract data and metadata
        result = result_list[0] if result_list else {"metadata": [], "data": []}
        words_data = result.get("data", [])
        total = result["metadata"][0]["total"] if result["metadata"] else 0

        # Generate fresh URLs for audio files
        for word in words_data:
            if word.get("object_name"):
                try:
                    new_url = user_tenant.minio.get_url(word["object_name"])
                    word["audio_url"] = new_url
                except Exception as e:
                    loggers.warning(f"Failed to generate new URL for {word['object_name']}: {e}")

            # Remove object_name from response as it's only used for URL generation
            word.pop("object_name", None)
            # Remove the MongoDB _id field
            word.pop("_id", None)

        # Calculate total pages
        total_pages = (total + limit - 1) // limit if total > 0 else 0

        return PaginationResponse[Dict[str, Any]](
            data=convert_object_ids(words_data),
            meta={
                "page": page,
                "limit": limit,
                "total": total,
                "total_pages": total_pages
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in /word-list endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
